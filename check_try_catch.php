<?php

/**
 * 批量检查 Services/Api 目录中所有服务文件的 public function 方法
 * 检查每个方法的第一行是否以 "try {" 开始
 */

class ServiceTryCatchChecker
{
    private $servicesPath;
    private $results = [];
    
    public function __construct()
    {
        $this->servicesPath = __DIR__ . '/php/api/app/Services/Api';
    }
    
    /**
     * 执行检查
     */
    public function check()
    {
        $files = $this->getServiceFiles();
        
        foreach ($files as $file) {
            $this->checkFile($file);
        }
        
        $this->generateReport();
    }
    
    /**
     * 获取所有服务文件
     */
    private function getServiceFiles()
    {
        $files = [];
        $directory = new DirectoryIterator($this->servicesPath);
        
        foreach ($directory as $fileInfo) {
            if ($fileInfo->isDot() || !$fileInfo->isFile()) {
                continue;
            }
            
            if (pathinfo($fileInfo->getFilename(), PATHINFO_EXTENSION) === 'php') {
                $files[] = $fileInfo->getPathname();
            }
        }
        
        sort($files);
        return $files;
    }
    
    /**
     * 检查单个文件
     */
    private function checkFile($filePath)
    {
        $fileName = basename($filePath);
        $content = file_get_contents($filePath);
        
        if ($content === false) {
            echo "无法读取文件: $fileName\n";
            return;
        }
        
        // 使用正则表达式查找所有 public function
        $pattern = '/public\s+function\s+([a-zA-Z_][a-zA-Z0-9_]*)\s*\([^)]*\)\s*(?::\s*[^{]+)?\s*\{([^}]*(?:\{[^}]*\}[^}]*)*)\}/s';
        
        preg_match_all($pattern, $content, $matches, PREG_SET_ORDER);
        
        $methodsWithoutTry = [];
        
        foreach ($matches as $match) {
            $methodName = $match[1];
            $methodBody = trim($match[2]);
            
            // 跳过构造函数
            if ($methodName === '__construct') {
                continue;
            }
            
            // 检查方法体的第一行是否以 "try {" 开始
            $firstLine = $this->getFirstNonEmptyLine($methodBody);
            
            if (!$this->startsWithTry($firstLine)) {
                $methodsWithoutTry[] = $methodName;
            }
        }
        
        if (!empty($methodsWithoutTry)) {
            $this->results[$fileName] = $methodsWithoutTry;
        }
        
        echo "已检查: $fileName (" . count($matches) . " 个方法)\n";
    }
    
    /**
     * 获取第一个非空行
     */
    private function getFirstNonEmptyLine($content)
    {
        $lines = explode("\n", $content);
        
        foreach ($lines as $line) {
            $trimmedLine = trim($line);
            if (!empty($trimmedLine)) {
                return $trimmedLine;
            }
        }
        
        return '';
    }
    
    /**
     * 检查是否以 try { 开始
     */
    private function startsWithTry($line)
    {
        return preg_match('/^\s*try\s*\{/', $line);
    }
    
    /**
     * 生成报告
     */
    private function generateReport()
    {
        echo "\n" . str_repeat("=", 80) . "\n";
        echo "检查结果报告\n";
        echo str_repeat("=", 80) . "\n\n";
        
        if (empty($this->results)) {
            echo "✅ 所有服务文件的 public function 方法都正确以 'try {' 开始！\n";
            return;
        }
        
        echo "❌ 发现以下方法未以 'try {' 开始：\n\n";
        
        $counter = 1;
        foreach ($this->results as $fileName => $methods) {
            echo "### {$counter}. {$fileName} (" . count($methods) . "个方法)\n";
            
            foreach ($methods as $method) {
                echo "- [ ] `{$method}()`\n";
            }
            
            echo "\n";
            $counter++;
        }
        
        // 统计信息
        $totalFiles = count($this->results);
        $totalMethods = array_sum(array_map('count', $this->results));
        
        echo str_repeat("-", 40) . "\n";
        echo "统计信息：\n";
        echo "- 需要修复的文件数：{$totalFiles}\n";
        echo "- 需要修复的方法数：{$totalMethods}\n";
    }
}

// 执行检查
$checker = new ServiceTryCatchChecker();
$checker->check();

?>
